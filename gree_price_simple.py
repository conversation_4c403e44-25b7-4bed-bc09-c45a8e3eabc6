# coding:gbk
"""
简化版本：获取格力电器20250430那天的9点35分收盘价
"""

def init(ContextInfo):
    """初始化"""
    print("开始获取格力电器股价数据...")
    # 设置股票池为格力电器
    ContextInfo.set_universe(['000651.SZ'])

def handlebar(ContextInfo):
    """主处理函数"""
    try:
        # 格力电器股票代码
        stock_code = '000651.SZ'
        
        # 目标日期和时间
        target_date = '20250430'
        start_time = target_date + ' 09:30:00'
        end_time = target_date + ' 10:00:00'
        
        print(f"正在获取{stock_code}在{target_date}的分钟数据...")
        
        # 获取分钟级历史数据
        data = ContextInfo.get_history_data(
            stock_code,    # 股票代码
            '1m',         # 1分钟K线
            start_time,   # 开始时间
            end_time      # 结束时间
        )
        
        if data is None:
            print("未获取到数据，可能是因为:")
            print("1. 该日期为非交易日")
            print("2. 数据尚未更新到2025年4月30日")
            print("3. 网络连接问题")
            return
        
        print(f"获取到{len(data)}条数据")
        
        # 查找9:35分的数据
        target_price = None
        for i, bar in enumerate(data):
            # 获取K线时间
            bar_time = ContextInfo.get_bar_timetag(i)
            time_str = timetag_to_datetime(bar_time, '%H:%M:%S')
            
            print(f"时间: {time_str}, 收盘价: {bar['close']}")
            
            # 检查是否是9:35分
            if '09:35' in time_str:
                target_price = bar['close']
                print(f"*** 找到目标数据！9:35分收盘价: {target_price}元 ***")
                
                # 保存数据
                save_result(target_price, target_date)
                break
        
        if target_price is None:
            print("未找到9:35分的精确数据")
            # 显示所有可用数据供参考
            print("可用的时间点数据:")
            for i, bar in enumerate(data):
                bar_time = ContextInfo.get_bar_timetag(i)
                time_str = timetag_to_datetime(bar_time, '%H:%M:%S')
                print(f"  {time_str}: {bar['close']}元")
                
    except Exception as e:
        print(f"发生错误: {e}")

def save_result(price, date):
    """保存结果到文件"""
    try:
        import datetime
        
        # 创建结果文本
        result_text = f"""
格力电器股价查询结果
==================
股票代码: 000651.SZ
股票名称: 格力电器
查询日期: {date}
查询时间: 09:35:00
收盘价格: {price}元
查询时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 保存到文件
        filename = f"格力电器_{date}_0935_价格.txt"
        with open(filename, 'w', encoding='gbk') as f:
            f.write(result_text)
        
        print(f"结果已保存到文件: {filename}")
        print(result_text)
        
    except Exception as e:
        print(f"保存文件时出错: {e}")

# 备用方法：如果上述方法不工作，可以尝试这个
def alternative_method(ContextInfo):
    """备用获取方法"""
    try:
        # 使用get_market_data获取最新数据
        stock_list = ['000651.SZ']
        field_list = ['close', 'open', 'high', 'low', 'volume']
        
        data = ContextInfo.get_market_data(field_list, stock_list)
        
        if data:
            close_price = data[0]  # 收盘价
            print(f"格力电器最新收盘价: {close_price}元")
            return close_price
        
    except Exception as e:
        print(f"备用方法出错: {e}")
        return None
