# coding:gbk
"""
获取格力电器20250430那天的9点35分收盘价并保存
使用QMT Python API
"""

import pandas as pd
from datetime import datetime
import os

def init(ContextInfo):
    """
    初始化函数
    """
    # 设置格力电器股票代码
    ContextInfo.stock_code = '000651.SZ'  # 格力电器股票代码
    ContextInfo.target_date = '20250430'  # 目标日期
    ContextInfo.target_time = '09:35:00'  # 目标时间
    ContextInfo.data_collected = False
    
    print("初始化完成，准备获取格力电器股价数据...")

def handlebar(ContextInfo):
    """
    主要处理函数
    """
    if ContextInfo.data_collected:
        return
    
    try:
        # 获取格力电器的历史数据
        stock_code = ContextInfo.stock_code
        target_date = ContextInfo.target_date
        target_time = ContextInfo.target_time
        
        print(f"正在获取{stock_code}在{target_date} {target_time}的数据...")
        
        # 使用get_history_data获取分钟级别的历史数据
        # 参数说明：股票代码，周期，起始时间，结束时间，复权方式，市场
        start_date = target_date + ' 09:30:00'  # 开盘时间
        end_date = target_date + ' 15:00:00'    # 收盘时间
        
        # 获取1分钟K线数据
        hist_data = ContextInfo.get_history_data(
            stock_code,     # 股票代码
            '1m',          # 1分钟周期
            start_date,    # 开始时间
            end_date,      # 结束时间
            'front'        # 前复权
        )
        
        if hist_data is None or len(hist_data) == 0:
            print(f"未能获取到{stock_code}在{target_date}的数据")
            return
        
        print(f"成功获取到{len(hist_data)}条数据")
        
        # 查找9:35分的数据
        target_price = None
        target_record = None
        
        for i, record in enumerate(hist_data):
            # 获取时间戳并转换为可读格式
            timestamp = record.get('time', 0)
            if timestamp > 0:
                # 将时间戳转换为datetime对象
                dt = datetime.fromtimestamp(timestamp / 1000)  # 假设时间戳是毫秒
                time_str = dt.strftime('%H:%M:%S')
                
                print(f"时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}, "
                      f"开盘: {record.get('open', 0)}, "
                      f"最高: {record.get('high', 0)}, "
                      f"最低: {record.get('low', 0)}, "
                      f"收盘: {record.get('close', 0)}, "
                      f"成交量: {record.get('volume', 0)}")
                
                # 检查是否是9:35分的数据
                if time_str == '09:35:00':
                    target_price = record.get('close', 0)
                    target_record = record
                    print(f"找到目标时间数据！9:35分收盘价: {target_price}")
                    break
        
        # 如果没有找到精确的9:35数据，寻找最接近的时间
        if target_price is None:
            print("未找到精确的9:35分数据，寻找最接近的时间...")
            for record in hist_data:
                timestamp = record.get('time', 0)
                if timestamp > 0:
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    if dt.hour == 9 and dt.minute >= 35:
                        target_price = record.get('close', 0)
                        target_record = record
                        print(f"找到最接近的数据时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}, 收盘价: {target_price}")
                        break
        
        # 保存数据到文件
        if target_price is not None:
            save_data_to_file(stock_code, target_date, target_time, target_price, target_record)
            ContextInfo.data_collected = True
        else:
            print("未能找到目标时间的股价数据")
            
    except Exception as e:
        print(f"获取数据时发生错误: {str(e)}")

def save_data_to_file(stock_code, date, time, price, record):
    """
    保存数据到文件
    """
    try:
        # 创建数据字典
        data = {
            '股票代码': stock_code,
            '股票名称': '格力电器',
            '日期': date,
            '时间': time,
            '收盘价': price,
            '开盘价': record.get('open', 0) if record else 0,
            '最高价': record.get('high', 0) if record else 0,
            '最低价': record.get('low', 0) if record else 0,
            '成交量': record.get('volume', 0) if record else 0,
            '成交额': record.get('amount', 0) if record else 0,
            '获取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 保存到CSV文件
        filename = f"格力电器_{date}_{time.replace(':', '')}_股价数据.csv"
        df = pd.DataFrame([data])
        df.to_csv(filename, index=False, encoding='gbk')
        
        # 保存到文本文件
        txt_filename = f"格力电器_{date}_{time.replace(':', '')}_股价数据.txt"
        with open(txt_filename, 'w', encoding='gbk') as f:
            f.write("格力电器股价数据\n")
            f.write("=" * 50 + "\n")
            for key, value in data.items():
                f.write(f"{key}: {value}\n")
        
        print(f"数据已保存到文件: {filename} 和 {txt_filename}")
        print(f"格力电器在{date} {time}的收盘价为: {price}元")
        
    except Exception as e:
        print(f"保存文件时发生错误: {str(e)}")

# 备用方法：使用get_market_data获取实时数据
def get_current_price(ContextInfo):
    """
    获取当前股价（备用方法）
    """
    try:
        stock_code = '000651.SZ'
        market_data = ContextInfo.get_market_data(['close'], [stock_code])
        
        if market_data and len(market_data) > 0:
            current_price = market_data[0]
            print(f"格力电器当前价格: {current_price}")
            return current_price
        else:
            print("无法获取当前股价")
            return None
            
    except Exception as e:
        print(f"获取当前股价时发生错误: {str(e)}")
        return None

if __name__ == "__main__":
    print("请在QMT平台中运行此脚本")
    print("脚本功能：获取格力电器20250430那天的9点35分收盘价并保存")
