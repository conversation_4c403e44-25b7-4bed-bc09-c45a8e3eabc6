# coding:gbk
"""
获取格力电器20250430那天的9点35分收盘价并保存
使用QMT Python API
"""

def init(ContextInfo):
    """
    初始化函数
    """
    # 设置格力电器股票代码
    ContextInfo.stock_code = '000651.SZ'  # 格力电器股票代码
    ContextInfo.target_date = '20250430'  # 目标日期
    ContextInfo.target_time = '09:35:00'  # 目标时间
    ContextInfo.data_collected = False

    print("初始化完成，准备获取格力电器股价数据...")

def handlebar(ContextInfo):
    """
    主要处理函数
    """
    if ContextInfo.data_collected:
        return

    try:
        # 获取格力电器的历史数据
        stock_code = ContextInfo.stock_code
        target_date = ContextInfo.target_date
        target_time = ContextInfo.target_time

        print("正在获取%s在%s %s的数据..." % (stock_code, target_date, target_time))

        # 使用get_history_data获取分钟级别的历史数据
        start_date = target_date + ' 09:30:00'  # 开盘时间
        end_date = target_date + ' 15:00:00'    # 收盘时间

        # 获取1分钟K线数据
        hist_data = ContextInfo.get_history_data(
            stock_code,     # 股票代码
            '1m',          # 1分钟周期
            start_date,    # 开始时间
            end_date       # 结束时间
        )

        if hist_data is None or len(hist_data) == 0:
            print("未能获取到%s在%s的数据" % (stock_code, target_date))
            print("可能原因：")
            print("1. 该日期为非交易日")
            print("2. 数据尚未更新到2025年")
            print("3. 请尝试使用历史日期测试")
            return

        print("成功获取到%d条数据" % len(hist_data))

        # 查找9:35分的数据
        target_price = None
        target_record = None

        # 遍历历史数据
        for i in range(len(hist_data)):
            # 获取当前K线的时间戳
            bar_time = ContextInfo.get_bar_timetag(i)

            # 将时间戳转换为可读格式
            time_str = timetag_to_datetime(bar_time, '%H:%M:%S')
            date_str = timetag_to_datetime(bar_time, '%Y-%m-%d')

            # 获取K线数据
            open_price = hist_data[i]['open']
            high_price = hist_data[i]['high']
            low_price = hist_data[i]['low']
            close_price = hist_data[i]['close']
            volume = hist_data[i]['volume']

            print("时间: %s %s, 开盘: %.2f, 最高: %.2f, 最低: %.2f, 收盘: %.2f, 成交量: %d" %
                  (date_str, time_str, open_price, high_price, low_price, close_price, volume))

            # 检查是否是9:35分的数据
            if '09:35' in time_str:
                target_price = close_price
                target_record = {
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume,
                    'time': bar_time
                }
                print("*** 找到目标时间数据！9:35分收盘价: %.2f ***" % target_price)
                break

        # 如果没有找到精确的9:35数据，寻找最接近的时间
        if target_price is None:
            print("未找到精确的9:35分数据，寻找最接近的时间...")
            for i in range(len(hist_data)):
                bar_time = ContextInfo.get_bar_timetag(i)
                time_str = timetag_to_datetime(bar_time, '%H:%M')

                if time_str >= '09:35':
                    target_price = hist_data[i]['close']
                    target_record = {
                        'open': hist_data[i]['open'],
                        'high': hist_data[i]['high'],
                        'low': hist_data[i]['low'],
                        'close': hist_data[i]['close'],
                        'volume': hist_data[i]['volume'],
                        'time': bar_time
                    }
                    full_time = timetag_to_datetime(bar_time, '%Y-%m-%d %H:%M:%S')
                    print("找到最接近的数据时间: %s, 收盘价: %.2f" % (full_time, target_price))
                    break

        # 保存数据到文件
        if target_price is not None:
            save_data_to_file(stock_code, target_date, target_time, target_price, target_record)
            ContextInfo.data_collected = True
        else:
            print("未能找到目标时间的股价数据")

    except Exception as e:
        print("获取数据时发生错误: %s" % str(e))

def save_data_to_file(stock_code, date, time, price, record):
    """
    保存数据到文件
    """
    try:
        # 获取当前时间字符串（简单方式）
        import time as time_module
        current_time = time_module.strftime('%Y-%m-%d %H:%M:%S')

        # 创建结果文本
        result_text = """格力电器股价数据
==================================================
股票代码: %s
股票名称: 格力电器
日期: %s
时间: %s
收盘价: %.2f
开盘价: %.2f
最高价: %.2f
最低价: %.2f
成交量: %d
获取时间: %s
""" % (
            stock_code,
            date,
            time,
            price,
            record.get('open', 0) if record else 0,
            record.get('high', 0) if record else 0,
            record.get('low', 0) if record else 0,
            record.get('volume', 0) if record else 0,
            current_time
        )

        # 保存到文本文件
        filename = "格力电器_%s_%s_股价数据.txt" % (date, time.replace(':', ''))

        try:
            with open(filename, 'w') as f:
                f.write(result_text)
            print("数据已保存到文件: %s" % filename)
        except:
            # 如果文件操作失败，至少在控制台显示结果
            print("文件保存失败，但数据获取成功：")

        print(result_text)
        print("格力电器在%s %s的收盘价为: %.2f元" % (date, time, price))

    except Exception as e:
        print("保存文件时发生错误: %s" % str(e))

# 备用方法：使用get_market_data获取实时数据
def get_current_price(ContextInfo):
    """
    获取当前股价（备用方法）
    """
    try:
        stock_code = '000651.SZ'
        market_data = ContextInfo.get_market_data(['close'], [stock_code])

        if market_data and len(market_data) > 0:
            current_price = market_data[0]
            print("格力电器当前价格: %.2f" % current_price)
            return current_price
        else:
            print("无法获取当前股价")
            return None

    except Exception as e:
        print("获取当前股价时发生错误: %s" % str(e))
        return None
