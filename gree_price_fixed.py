# coding:gbk
"""
修正版本：获取格力电器股价数据
避免使用可能不存在的函数，使用更稳定的方法
"""

def init(ContextInfo):
    """初始化函数"""
    print("=== 格力电器股价获取脚本启动 ===")
    print("股票代码: 000651.SZ")
    print("目标日期: 2025年4月30日")
    print("目标时间: 9:35")
    
    # 设置股票池
    ContextInfo.set_universe(['000651.SZ'])
    
    # 标记是否已获取数据
    ContextInfo.data_fetched = False

def handlebar(ContextInfo):
    """主处理函数"""
    
    # 避免重复执行
    if hasattr(ContextInfo, 'data_fetched') and ContextInfo.data_fetched:
        return
    
    try:
        stock_code = '000651.SZ'
        print("开始获取格力电器历史数据...")
        
        # 方法1：尝试获取历史数据
        try_get_history_data(ContextInfo, stock_code)
        
        # 方法2：如果历史数据获取失败，尝试获取当前数据
        try_get_current_data(ContextInfo, stock_code)
        
        ContextInfo.data_fetched = True
        
    except Exception as e:
        print("主函数执行出错: %s" % str(e))

def try_get_history_data(ContextInfo, stock_code):
    """尝试获取历史数据"""
    try:
        print("方法1: 尝试获取历史分钟数据...")
        
        # 设置日期范围
        start_date = '20250430 09:30:00'
        end_date = '20250430 10:00:00'
        
        # 获取历史数据
        hist_data = ContextInfo.get_history_data(stock_code, '1m', start_date, end_date)
        
        if hist_data is None:
            print("历史数据获取失败 - 可能原因:")
            print("1. 2025年4月30日数据尚未存在")
            print("2. 该日期为非交易日")
            print("3. 数据权限不足")
            return False
            
        if len(hist_data) == 0:
            print("历史数据为空")
            return False
            
        print("成功获取历史数据，共 %d 条记录" % len(hist_data))
        
        # 查找目标时间的数据
        found_data = False
        for i, bar in enumerate(hist_data):
            try:
                # 尝试不同的方式获取时间信息
                if hasattr(ContextInfo, 'get_bar_timetag'):
                    bar_time = ContextInfo.get_bar_timetag(i)
                    # 这里简化处理，直接使用索引来估算时间
                    estimated_minute = 30 + i  # 从9:30开始，每分钟递增
                    time_str = "09:%02d" % estimated_minute
                else:
                    # 如果无法获取精确时间，使用索引估算
                    estimated_minute = 30 + i
                    time_str = "09:%02d" % estimated_minute
                
                close_price = bar.get('close', 0)
                open_price = bar.get('open', 0)
                high_price = bar.get('high', 0)
                low_price = bar.get('low', 0)
                volume = bar.get('volume', 0)
                
                print("时间: %s, 开盘: %.2f, 最高: %.2f, 最低: %.2f, 收盘: %.2f, 成交量: %d" % 
                      (time_str, open_price, high_price, low_price, close_price, volume))
                
                # 检查是否是目标时间附近
                if '09:35' in time_str or estimated_minute == 35:
                    print("*** 找到目标时间数据！***")
                    print("9:35分收盘价: %.2f元" % close_price)
                    save_result(close_price, '20250430', '09:35:00', bar)
                    found_data = True
                    break
                    
            except Exception as e:
                print("处理第%d条数据时出错: %s" % (i, str(e)))
                continue
        
        if not found_data:
            print("未找到9:35分的精确数据")
            # 如果有数据但没找到精确时间，显示第一条数据作为参考
            if len(hist_data) > 0:
                first_bar = hist_data[0]
                print("参考数据 - 第一条记录的收盘价: %.2f元" % first_bar.get('close', 0))
                save_result(first_bar.get('close', 0), '20250430', '09:30:00', first_bar)
        
        return found_data
        
    except Exception as e:
        print("获取历史数据时出错: %s" % str(e))
        return False

def try_get_current_data(ContextInfo, stock_code):
    """尝试获取当前数据作为备用"""
    try:
        print("方法2: 尝试获取当前行情数据...")
        
        # 获取当前行情数据
        market_data = ContextInfo.get_market_data(['close', 'open', 'high', 'low', 'volume'], [stock_code])
        
        if market_data and len(market_data) >= 5:
            close_price = market_data[0]
            open_price = market_data[1] 
            high_price = market_data[2]
            low_price = market_data[3]
            volume = market_data[4]
            
            print("当前行情数据:")
            print("开盘: %.2f, 最高: %.2f, 最低: %.2f, 收盘: %.2f, 成交量: %d" % 
                  (open_price, high_price, low_price, close_price, volume))
            
            # 保存当前数据作为参考
            current_data = {
                'open': open_price,
                'high': high_price, 
                'low': low_price,
                'close': close_price,
                'volume': volume
            }
            
            print("注意：这是当前行情数据，不是2025年4月30日的数据")
            save_result(close_price, 'current', 'current', current_data)
            return True
        else:
            print("无法获取当前行情数据")
            return False
            
    except Exception as e:
        print("获取当前数据时出错: %s" % str(e))
        return False

def save_result(price, date, time, data):
    """保存结果"""
    try:
        result_text = """
=== 格力电器股价查询结果 ===
股票代码: 000651.SZ
股票名称: 格力电器
查询日期: %s
查询时间: %s
收盘价格: %.2f元
开盘价格: %.2f元
最高价格: %.2f元
最低价格: %.2f元
成交量: %d
=============================
""" % (
            date,
            time, 
            price,
            data.get('open', 0),
            data.get('high', 0),
            data.get('low', 0),
            data.get('volume', 0)
        )
        
        print(result_text)
        
        # 尝试保存到文件
        try:
            filename = "格力电器股价_%s_%s.txt" % (date, time.replace(':', ''))
            with open(filename, 'w') as f:
                f.write(result_text)
            print("结果已保存到文件: %s" % filename)
        except:
            print("文件保存失败，但数据显示成功")
            
    except Exception as e:
        print("保存结果时出错: %s" % str(e))

# 测试用的简化版本
def simple_test(ContextInfo):
    """简单测试函数"""
    try:
        print("=== 简单测试 ===")
        stock_code = '000651.SZ'
        
        # 获取基本行情数据
        data = ContextInfo.get_market_data(['close'], [stock_code])
        if data:
            print("格力电器当前收盘价: %.2f元" % data[0])
        else:
            print("无法获取行情数据")
            
    except Exception as e:
        print("测试出错: %s" % str(e))
