# coding:gbk
"""
最简单的测试版本 - 获取格力电器股价
确保在QMT中能正常运行
"""

def init(ContextInfo):
    """初始化函数"""
    print("=== 格力电器股价获取测试 ===")
    print("正在初始化...")
    
    # 设置股票代码
    ContextInfo.stock_code = '000651.SZ'
    ContextInfo.test_completed = False

def handlebar(ContextInfo):
    """主处理函数"""
    
    # 避免重复执行
    if ContextInfo.test_completed:
        return
        
    print("开始获取格力电器股价数据...")
    
    try:
        stock_code = ContextInfo.stock_code
        print("股票代码: %s" % stock_code)
        
        # 测试1: 获取当前行情数据
        print("\n=== 测试1: 获取当前行情 ===")
        test_current_market(ContextInfo, stock_code)
        
        # 测试2: 尝试获取历史数据
        print("\n=== 测试2: 尝试获取历史数据 ===")
        test_history_data(ContextInfo, stock_code)
        
        ContextInfo.test_completed = True
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print("执行出错: %s" % str(e))

def test_current_market(ContextInfo, stock_code):
    """测试获取当前行情"""
    try:
        # 方法1: 获取收盘价
        data1 = ContextInfo.get_market_data(['close'], [stock_code])
        if data1 and len(data1) > 0:
            print("当前收盘价: %.2f元" % data1[0])
        else:
            print("方法1失败: 无法获取收盘价")
        
        # 方法2: 获取多个字段
        data2 = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], [stock_code])
        if data2 and len(data2) >= 5:
            print("开盘价: %.2f元" % data2[0])
            print("最高价: %.2f元" % data2[1]) 
            print("最低价: %.2f元" % data2[2])
            print("收盘价: %.2f元" % data2[3])
            print("成交量: %d" % data2[4])
            
            # 保存当前数据
            save_current_data(data2)
        else:
            print("方法2失败: 无法获取完整行情数据")
            
    except Exception as e:
        print("获取当前行情出错: %s" % str(e))

def test_history_data(ContextInfo, stock_code):
    """测试获取历史数据"""
    try:
        # 尝试获取今天的数据
        print("尝试获取今天的分钟数据...")
        
        # 先尝试获取较近的日期
        test_dates = [
            '20241201 09:30:00',  # 2024年12月1日
            '20241130 09:30:00',  # 2024年11月30日
            '20241129 09:30:00'   # 2024年11月29日
        ]
        
        for start_date in test_dates:
            end_date = start_date.replace('09:30:00', '10:00:00')
            print("尝试日期: %s" % start_date[:8])
            
            try:
                hist_data = ContextInfo.get_history_data(stock_code, '1m', start_date, end_date)
                
                if hist_data and len(hist_data) > 0:
                    print("成功获取历史数据，共 %d 条" % len(hist_data))
                    
                    # 显示前几条数据
                    for i in range(min(5, len(hist_data))):
                        bar = hist_data[i]
                        print("第%d条: 开盘%.2f 最高%.2f 最低%.2f 收盘%.2f 成交量%d" % 
                              (i+1, bar.get('open', 0), bar.get('high', 0), 
                               bar.get('low', 0), bar.get('close', 0), bar.get('volume', 0)))
                    
                    # 如果找到数据就停止尝试其他日期
                    break
                else:
                    print("该日期无数据")
                    
            except Exception as e:
                print("获取 %s 数据出错: %s" % (start_date[:8], str(e)))
                continue
        
        # 尝试获取2025年4月30日的数据（目标日期）
        print("\n尝试获取目标日期 2025年4月30日 的数据...")
        try:
            target_start = '20250430 09:30:00'
            target_end = '20250430 10:00:00'
            
            target_data = ContextInfo.get_history_data(stock_code, '1m', target_start, target_end)
            
            if target_data and len(target_data) > 0:
                print("*** 成功获取2025年4月30日数据！***")
                print("共 %d 条记录" % len(target_data))
                
                # 查找9:35分的数据
                for i, bar in enumerate(target_data):
                    # 假设第6条数据是9:35分（9:30, 9:31, 9:32, 9:33, 9:34, 9:35）
                    if i == 5:  # 索引5对应第6条，即9:35分
                        close_price = bar.get('close', 0)
                        print("*** 9:35分收盘价: %.2f元 ***" % close_price)
                        save_target_data(close_price, bar)
                        break
            else:
                print("2025年4月30日数据不存在（预期结果，因为日期未到）")
                
        except Exception as e:
            print("获取2025年4月30日数据出错: %s" % str(e))
            
    except Exception as e:
        print("历史数据测试出错: %s" % str(e))

def save_current_data(data):
    """保存当前数据"""
    try:
        result = """
格力电器当前行情数据
==================
股票代码: 000651.SZ
股票名称: 格力电器
开盘价: %.2f元
最高价: %.2f元  
最低价: %.2f元
收盘价: %.2f元
成交量: %d
注意: 这是当前实时数据，非2025年4月30日数据
""" % (data[0], data[1], data[2], data[3], data[4])
        
        print(result)
        
        # 尝试保存文件
        try:
            with open("格力电器_当前行情.txt", "w") as f:
                f.write(result)
            print("当前行情已保存到文件")
        except:
            print("文件保存失败")
            
    except Exception as e:
        print("保存当前数据出错: %s" % str(e))

def save_target_data(price, data):
    """保存目标数据"""
    try:
        result = """
格力电器目标时间股价
==================
股票代码: 000651.SZ
股票名称: 格力电器
日期: 2025年4月30日
时间: 9:35分
收盘价: %.2f元
开盘价: %.2f元
最高价: %.2f元
最低价: %.2f元
成交量: %d
==================
""" % (price, data.get('open', 0), data.get('high', 0), 
       data.get('low', 0), data.get('volume', 0))
        
        print(result)
        
        # 尝试保存文件
        try:
            with open("格力电器_20250430_0935.txt", "w") as f:
                f.write(result)
            print("目标数据已保存到文件: 格力电器_20250430_0935.txt")
        except:
            print("文件保存失败，但数据获取成功")
            
    except Exception as e:
        print("保存目标数据出错: %s" % str(e))
