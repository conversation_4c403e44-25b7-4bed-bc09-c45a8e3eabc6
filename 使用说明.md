# 格力电器股价获取脚本使用说明

## 功能描述
本脚本用于获取格力电器(000651.SZ)在2025年4月30日9点35分的收盘价，并将结果保存到文件中。

## 文件说明

### 1. get_gree_stock_price.py
- **功能**: 完整版本的股价获取脚本（已修正）
- **特点**: 包含详细的错误处理和数据保存功能
- **输出**: TXT文件

### 2. gree_price_simple.py
- **功能**: 简化版本的股价获取脚本
- **特点**: 代码简洁，易于理解和修改
- **输出**: TXT文件

### 3. gree_price_fixed.py
- **功能**: 修正版本，避免使用可能不存在的函数
- **特点**: 更稳定，包含多种获取方法
- **推荐**: 如果其他版本出错，建议使用此版本

### 4. gree_simple_test.py ⭐ **推荐使用**
- **功能**: 最简单的测试版本
- **特点**: 确保在QMT中能正常运行，包含详细测试
- **输出**: 控制台输出和TXT文件

## 快速使用指南 ⚡

**推荐使用 `gree_simple_test.py`，最稳定可靠！**

### 快速步骤：
1. 打开QMT软件
2. 新建Python策略
3. 复制 `gree_simple_test.py` 的代码
4. 点击"运行"
5. 查看控制台输出结果

## 详细使用步骤

### 1. 准备工作
1. 确保已安装QMT交易软件
2. 确保有有效的QMT账号和数据权限
3. 将脚本文件复制到QMT可访问的目录

### 2. 在QMT中运行脚本
1. 打开QMT软件
2. 进入策略开发界面
3. 新建Python策略
4. 将脚本代码复制到编辑器中
5. 点击"运行"按钮

### 3. 查看结果
- 脚本运行后会在控制台输出结果
- 同时会生成包含股价数据的文件

## 重要说明

### 关于日期 2025年4月30日
**注意**: 由于当前时间还未到达2025年4月30日，该日期的数据可能无法获取。建议：

1. **测试用途**: 可以修改脚本中的日期为历史日期进行测试
2. **实际使用**: 等到2025年4月30日之后再运行脚本
3. **替代方案**: 使用历史日期验证脚本功能

### 修改日期示例
如果要获取其他日期的数据，请修改以下部分：

```python
# 在脚本中找到这一行
target_date = '20250430'  # 修改为所需日期，格式：YYYYMMDD

# 例如获取2024年12月1日的数据
target_date = '20241201'
```

### 修改时间示例
如果要获取其他时间点的数据：

```python
# 修改目标时间
target_time = '09:35:00'  # 修改为所需时间，格式：HH:MM:SS

# 例如获取10:30分的数据
target_time = '10:30:00'
```

## 可能遇到的问题及解决方案

### 1. 导入模块错误
**错误信息**: `"pandas" is not defined` 或 `"datetime" is not defined`
**解决方案**:
- 使用 `gree_simple_test.py`（推荐）
- 该版本不依赖外部模块，更稳定

### 2. 函数未定义错误
**错误信息**: `"timetag_to_datetime" is not defined`
**解决方案**:
- 使用 `gree_price_fixed.py` 或 `gree_simple_test.py`
- 这些版本避免了可能不存在的函数

### 3. 数据获取失败
**原因**:
- 指定日期为非交易日
- 数据尚未更新到2025年
- 网络连接问题
- 权限不足

**解决方案**:
- 使用 `gree_simple_test.py` 进行测试
- 该脚本会尝试多个日期和方法
- 检查控制台输出的详细信息

### 4. 2025年4月30日数据不存在
**原因**: 该日期尚未到来
**解决方案**:
- 这是正常现象
- 脚本会显示当前可用数据作为参考
- 可以修改日期为历史日期进行测试

### 5. 时间点数据不精确
**原因**:
- QMT的时间函数可能不可用
- 数据精度问题

**解决方案**:
- 脚本使用索引估算时间
- 查看控制台输出的所有可用数据

### 6. 文件保存失败
**原因**:
- 文件路径权限问题
- 磁盘空间不足

**解决方案**:
- 数据仍会在控制台显示
- 确保有写入权限
- 检查磁盘空间

## 输出文件格式

### CSV文件内容
```
股票代码,股票名称,日期,时间,收盘价,开盘价,最高价,最低价,成交量,成交额,获取时间
000651.SZ,格力电器,20250430,09:35:00,XX.XX,XX.XX,XX.XX,XX.XX,XXXXX,XXXXXXX,2024-XX-XX XX:XX:XX
```

### TXT文件内容
```
格力电器股价数据
==================================================
股票代码: 000651.SZ
股票名称: 格力电器
日期: 20250430
时间: 09:35:00
收盘价: XX.XX
开盘价: XX.XX
最高价: XX.XX
最低价: XX.XX
成交量: XXXXX
成交额: XXXXXXX
获取时间: 2024-XX-XX XX:XX:XX
```

## 技术支持

如果在使用过程中遇到问题，请检查：
1. QMT软件版本是否最新
2. Python环境是否正常
3. 数据权限是否充足
4. 网络连接是否稳定

## 免责声明

本脚本仅用于技术学习和数据获取，不构成投资建议。使用者应当：
1. 遵守相关法律法规
2. 承担使用风险
3. 验证数据准确性
4. 谨慎进行投资决策
